#include <stdio.h>
#include <stdlib.h>
#include <time.h>

#define BILLION 1000000000L

void run_test(size_t array_size_bytes) {
    size_t num_elements = array_size_bytes / sizeof(int);
    int *a = malloc(array_size_bytes);

    if (!a) {
        fprintf(stderr, "Memory allocation failed\n");
        return;
    }

    for (size_t i = 0; i < num_elements; i++) {
        a[i] = 0;
    }

    size_t K = (1L << 30) / array_size_bytes; // 保证总访问量大致相同（1GB 总访问）

    struct timespec start, end;
    clock_gettime(CLOCK_MONOTONIC, &start);

    for (size_t t = 0; t < K; t++) {
        for (size_t i = 0; i < num_elements; i++) {
            a[i]++;
        }
    }

    clock_gettime(CLOCK_MONOTONIC, &end);

    double elapsed = (end.tv_sec - start.tv_sec) +
                     (end.tv_nsec - start.tv_nsec) / (double)BILLION;

    double total_ops = (double)num_elements * K;
    double gops = total_ops / 1e9 / elapsed;

    printf("Size = %6zu KB, Time = %.3f sec, Rate = %.2f GOPS\n",
           array_size_bytes / 1024, elapsed, gops);

    free(a);
}

int main() {
    for (int exp = 10; exp <= 24; exp++) { // 从 1KB 到 16MB
        size_t size = 1L << exp;
        run_test(size);
    }
    return 0;
}