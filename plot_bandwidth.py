import matplotlib.pyplot as plt
import numpy as np
import re

# 从 result.txt 文件读取数据
def read_benchmark_data(filename):
    """从结果文件中读取基准测试数据"""
    sizes_kb = []
    gops = []

    try:
        with open(filename, 'r') as file:
            for line in file:
                line = line.strip()
                if not line:  # 跳过空行
                    continue

                # 使用正则表达式解析每行数据
                # 格式: Size = 1024 KB, Time = 0.018 sec, Rate = 15.32 GOPS
                match = re.search(r'Size\s*=\s*(\d+)\s*KB.*Rate\s*=\s*([\d.]+)\s*GOPS', line)
                if match:
                    size = int(match.group(1))
                    rate = float(match.group(2))
                    sizes_kb.append(size)
                    gops.append(rate)

    except FileNotFoundError:
        print(f"错误：找不到文件 {filename}")
        print("请确保 result.txt 文件存在于当前目录中")
        return [], []
    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        return [], []

    return sizes_kb, gops

# 从 result.txt 读取数据
sizes_kb, gops = read_benchmark_data('result.txt')

plt.figure(figsize=(8, 5))
plt.plot(sizes_kb, gops, marker='o', linewidth=2)
plt.xscale('log', base=2)
plt.xlabel("Array Size (KB, log2 scale)")
plt.ylabel("Increments per second (GOPS)")
plt.title("Memory Bandwidth Benchmark")
plt.grid(True)
plt.tight_layout()
plt.savefig("bandwidth_plot.png")
plt.show()