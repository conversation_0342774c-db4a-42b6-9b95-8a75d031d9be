import matplotlib.pyplot as plt
import numpy as np

# 手动将 cache_test 程序输出复制进下面两个数组
sizes_kb = [1 << i for i in range(10, 25)]  # 1KB 到 16MB
gops = [
    14.2, 14.0, 13.8, 13.6, 13.3, 13.0, 12.5, 12.0, 11.5, 11.0,
    10.5, 9.5, 8.0, 6.5, 5.0  # 用你自己的实际结果替换
]

plt.figure(figsize=(8, 5))
plt.plot(sizes_kb, gops, marker='o', linewidth=2)
plt.xscale('log', base=2)
plt.xlabel("Array Size (KB, log2 scale)")
plt.ylabel("Increments per second (GOPS)")
plt.title("Memory Bandwidth Benchmark")
plt.grid(True)
plt.tight_layout()
plt.savefig("bandwidth_plot.png")
plt.show()