# Memory Bandwidth Benchmark

This project benchmarks memory subsystem performance by incrementing elements of an integer array of increasing sizes. It reveals how L1, L2, L3 cache and main memory affect bandwidth.

## Contents

- `cache_test.c` — Core benchmark in C
- `plot_bandwidth.py` — Script to visualize the results
- `Makefile` — Build and run the benchmark easily
- `README.md` — This file

## Usage

### 1. Build the benchmark
```bash
make
```

### 2. Run the benchmark
```bash
make run
```

### 3. Plot the results
Edit `plot_bandwidth.py` to paste your actual results and run:
```bash
python3 plot_bandwidth.py
```

### 4. Optionally, run with performance counters
```bash
perf stat ./cache_test
```

## Sample Output

```
Size =  1024 KB, Time = 0.030 sec, Rate = 10.23 GOPS
...
```